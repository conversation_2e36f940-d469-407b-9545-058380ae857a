import uuid

from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


class APIKey(models.Model):
    key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="api_keys")
    active = models.BooleanField(default=True)

    # Type annotations for Django's automatic attributes
    objects: models.Manager["APIKey"]

    class Meta:
        verbose_name = "API Key"
        verbose_name_plural = "API Keys"

    def __str__(self):
        return f"APIKey for {self.user} ({self.active})"
