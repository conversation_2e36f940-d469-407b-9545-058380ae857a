import logging
import traceback
from typing import TYPE_CHECKING, Annotated, List

import ninja
from django.http import HttpRequest, HttpResponse, JsonResponse
from ninja.security import APIKeyHeader
from ninja.types import DictStrAny
from pydantic import StringConstraints, ValidationError

from configurator.models import APIKey

if TYPE_CHECKING:
    from ninja import NinjaAPI

logger = logging.getLogger(__name__)


class APIKeyAuth(APIKeyHeader):
    param_name = "X-API-Key"

    def authenticate(self, _, key):
        try:
            key = APIKey.objects.get(key=key)
            if key.active:
                return key.user
        except APIKey.DoesNotExist:
            pass

        return None


NonEmptyString = Annotated[str, StringConstraints(min_length=1, strip_whitespace=True)]


NinjaValidationErrors = List[DictStrAny]


def serialize_pydantic_validation_error(validation_error: ValidationError):
    error = validation_error.errors()[0]
    return {"msg": error.get("msg", ""), "type": error.get("type", "")}


def patch_ninja_404_handler():
    def patched_default_404(request: HttpRequest, exc: Exception, api: "NinjaAPI") -> HttpResponse:
        logger.debug(f"Patched Ninja 404 handler called with exception: {exc}")
        return api.create_response(request, {"detail": str(exc)}, status=404)

    ninja.errors._default_404 = patched_default_404
    logger.info("Successfully patched Django Ninja's _default_404 function")


def patch_ninja_default_exception_handler():
    def _default_exception(request: HttpRequest, exc: Exception, api: "NinjaAPI") -> HttpResponse:
        logger.exception(exc)

        detail = str(exc)
        return JsonResponse({"detail": detail, "traceback": traceback.format_exc().replace('"', "'")}, status=500)

    ninja.errors._default_exception = _default_exception
    logger.info("Successfully patched Django Ninja's _default_exception function")


def patch_ninja_api_error_handlers():
    patch_ninja_404_handler()
    patch_ninja_default_exception_handler()
