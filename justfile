#!/usr/bin/env just

set shell := ["bash", "-c"]

default:
    @just --list

#####################
# LOCAL DEVELOPMENT #
#####################

dev-venv:
    -rm -fr .venv
    uv venv
    uv pip install --group dev --group test --compile-bytecode
    rm -fr hallite_configurator.egg-info build

dev-dependencies:
    uv pip install --group dev --group test --compile-bytecode
    rm -fr hallite_configurator.egg-info build

dev-server:
    doppler run -- uv run manage.py runserver_plus [::]:8080


#######
# APP #
#######

app-install-dependencies:
    uv pip install -e .
    uv lock
    rm -fr hallite_configurator_api.egg-info build

app-upgrade-dependencies:
    uv lock --upgrade

pre-commit:
    uv run pre-commit run --all-files

app-load-base-data:
     uv run manage.py loaddata config/fixtures/group.yaml
     uv run manage.py loaddata config/fixtures/user.yaml
     uv run manage.py loaddata config/fixtures/admin_interface.yaml


##########
# CHECKS #
##########

check:
    just check-black
    just check-isort
    just check-flake8
    just check-pylint
    just check-js-css
    just check-autoflake
    just check-types

check-djlint:
    uv run djlint ./ --check

check-flake8:
    uv run flake8 ./

check-isort:
    uv run isort ./ --check

check-black:
    uv run black ./ --check

check-pylint:
    uv run pylint --load-plugins pylint_django ./

check-autoflake:
    uv run autoflake --check --quiet --recursive ./

check-types:
    uv run ty check


##########
# FORMAT #
##########

format:
    just format-black
    just format-isort
    just format-autoflake
    just format-whitespace

format-isort:
    uv run isort ./

format-black:
    uv run black ./

format-autoflake:
    uv run autoflake --in-place --recursive ./

format-whitespace:
    find . -name "*.py" -not -path "./.venv/*" -not -path "./node_modules/*" -exec sed -i '' 's/[[:space:]]*$//' {} \;


###########
# TESTING #
###########

# Run tests with default settings from pyproject.toml
test *args='':
    uv run pytest -v "{{ args }}"

# Run tests with debug logging
test-verbose *args='':
    uv run pytest --log-cli-level=DEBUG "{{ args }}"

# Run tests with coverage reporting
test-coverage:
    uv run pytest --log-cli-level=DEBUG --cov --cov-config=pyproject.toml --cov-report=term --cov-report=html


##########
# CI/CD #
#########

git-version:
    #!/usr/bin/env bash
    set -e
    # Try to get the exact tag for the current commit
    TAG=$(git describe --tags --exact-match HEAD 2>/dev/null || echo "")

    if [ -n "$TAG" ]; then
        # Strip leading 'v' if present
        echo "${TAG#v}"
    else
        # Fallback to branch-commit if no exact tag is found
        BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        echo "${BRANCH}-${COMMIT}"
    fi

tag-release:
    #!/usr/bin/env bash
    set -e
    echo "Checking current branch..."
    BRANCH=$(git rev-parse --abbrev-ref HEAD)
    if [ "$BRANCH" != "main" ]; then
        echo "Not on main branch. Current branch is $BRANCH.";
        exit 1;
    fi
    echo "Checking for uncommitted changes..."
    if ! git diff --quiet HEAD; then
        echo "There are uncommitted changes.";
        exit 1;
    fi
    echo "Checking for staged changes..."
    if ! git diff --cached --quiet; then
        echo "There are staged changes.";
        exit 1;
    fi
    read -p "Enter version (last $(git describe --tags --abbrev=0)): " VERSION;

    echo "Checking if tag v$VERSION already exists..."
    if [ -n "$(git tag -l "v$VERSION")" ]; then
        echo "Error: Tag v$VERSION already exists."
        # Optional: Revert pyproject.toml change if tag exists and we are aborting
        # git checkout -- pyproject.toml
        exit 1;
    fi

    echo "Locking dependencies with uv..."
    uv lock

    echo "Tagging release v$VERSION..."
    git tag "v$VERSION"

    echo "Pushing tag v$VERSION to origin..."
    git push origin "v$VERSION"


##############
# DEPLOYMENT #
##############

tls:
	@doppler secrets get TLS_CERT --plain > tls/cert.pem
	@doppler secrets get TLS_KEY --plain > tls/key.pem

up *args='':
    @just tls
    @doppler run -- uv run gunicorn -c python:config.gunicorn config.wsgi --daemon {{ args }}

down:
    @pkill -f gunicorn || true
