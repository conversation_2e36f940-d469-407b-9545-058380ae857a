name: Test
permissions:
  contents: read
  packages: read

on:
  workflow_dispatch:
  push:
    branches:
      - main
  pull_request:
  workflow_call:
    outputs:
      test_success:
        value: ${{ jobs.test.outputs.success }}

jobs:
  test:
    runs-on: ubuntu-latest
    outputs:
      success: ${{ steps.set_test_status.outputs.success }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache-dependency-path: |
            uv.lock
            pyproject.toml

      - name: Configure Git for private repositories
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
        run: git config --global url."https://${GH_TOKEN}:@github.com/".insteadOf "https://github.com/"

      - name: Install dependencies
        run: |
          uv venv
          uv sync --group test

      - name: Install Doppler CLI
        uses: dopplerhq/cli-action@v3

      - name: Run tests
        env:
            DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN }}
        id: run_tests
        run: |
          doppler run --config dev_test -- uv run py.test --log-cli-level=DEBUG --cov --cov-config=pyproject.toml --cov-report=term --cov-report=html

      - name: Set test status output
        id: set_test_status
        run: |
          if [[ "${{ steps.run_tests.outcome }}" == "success" ]]; then
            echo "success=true" >> $GITHUB_OUTPUT
          else
            echo "success=false" >> $GITHUB_OUTPUT
          fi

      # - name: Upload test artifacts
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: test-results
      #     path: |
      #       .pytest_cache
      #       htmlcov
      #       .coverage


