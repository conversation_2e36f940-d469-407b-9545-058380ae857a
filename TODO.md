# Excel API Development Plan

## Important: Implementation Guidance

**Before implementing any step in this plan, developers and AI agents should refer to `AGENT.md` for detailed implementation guidance, coding standards, and project-specific requirements.**

The `AGENT.md` file contains:
- Project structure and conventions
- Coding standards and best practices
- Testing approaches and requirements
- Configuration and environment setup
- Any project-specific constraints or preferences

This TODO.md provides the high-level plan and step-by-step prompts, while `AGENT.md` provides the detailed implementation context needed to execute each step correctly.

---

## Project Overview

Building a REST API that automates Excel workbook operations for the Hallite Seal Estimator. The API will:
- Accept form data via REST endpoints
- Write values to Excel cells using xlwings
- Execute Excel macros for calculations
- Return structured JSON responses with pricing and part information
- Manage workbook sessions with proper lifecycle management
- Handle errors and resource constraints

## Architecture Summary

- **Framework**: Django 5.2+ with Django Ninja for REST API
- **Excel Automation**: xlwings for workbook manipulation
- **Error Handling**: pywinauto for VBA dialog capture
- **Session Management**: UUID-based temporary workbook copies
- **Resource Management**: RAM monitoring with email alerts

## Development Phases

### Phase 1: Core Excel Integration (Foundation)
Build the fundamental Excel automation capabilities without REST API complexity.

### Phase 2: Resource Management & Session Handling
Implement workbook lifecycle, session management, and resource monitoring.

### Phase 3: REST API Implementation
Create the Django Ninja endpoints with proper validation and error handling.

### Phase 4: Integration & Testing
Wire everything together and ensure robust operation.

---

## Detailed Implementation Steps

### Phase 1: Core Excel Integration

#### Step 1.1: Excel Service Foundation
```
Create the core Excel automation service that handles basic workbook operations.

Requirements:
- Create an ExcelService class that can open, manipulate, and close Excel workbooks
- Implement methods for reading from and writing to specific cells
- Add support for executing the 'runme' macro with proper error handling
- Include basic logging for debugging Excel operations
- Handle COM errors and Excel process management

Key components to implement:
1. ExcelService class with methods:
   - open_workbook(path) -> workbook handle
   - write_cell(workbook, cell_name, value)
   - read_cell(workbook, cell_name) -> value
   - execute_macro(workbook, macro_name)
   - close_workbook(workbook)
   - cleanup_excel_processes()

2. Basic error handling for:
   - Excel not available
   - Workbook file not found
   - Cell reference errors
   - Macro execution failures

3. Configuration management:
   - Read WORKBOOK_PATH from environment
   - Set up logging configuration

Implementation notes:
- Use xlwings for Excel automation
- Implement proper COM object cleanup
- Add delays between cell writes as specified (200ms)
- Set Application.DisplayAlerts=False during macro execution
- Include timeout handling for macro execution (5 second limit)

Testing approach:
- Create unit tests that mock xlwings
- Create integration tests with a test Excel file
- Test error scenarios (missing files, invalid cells, etc.)
```

#### Step 1.2: Workbook State Management
```
Create a WorkbookState service that represents the current state of the Excel workbook and provides high-level operations for the business logic.

Requirements:
- Create a WorkbookState class that abstracts Excel cell operations into business concepts
- Implement methods for writing input values in the correct sequence
- Add methods for reading all output values after macro execution
- Include validation logic based on workbook state
- Handle the specific error conditions defined in the spec

Key components to implement:
1. WorkbookState class with methods:
   - write_inputs(profile, measure, od, id, ch, extra, materials)
   - execute_calculation() -> calls runme macro
   - read_outputs() -> returns structured data
   - validate_state() -> checks for error conditions
   - get_error_message() -> extracts error from partdesc if needed

2. Input writing sequence (with 200ms delays):
   - profileentry → measureentry → ODentry → IDentry → CHentry
   - extratype → extraentry → MATL1entry → MATL2entry → MATL3entry

3. Output reading from cells:
   - Profile outputs: profileentry, C7, measureentry, etc.
   - Part info: C31, partdesc
   - Pricing: costpriceevco, C19, C20, C21
   - Materials: billetreqd1-3, mctime1-3, mmreqd1-3

4. Error detection logic:
   - Check costpriceevco == 0 for sheet errors
   - Check partdesc for specific error messages
   - Handle "refresh data" scenario with automatic retry
   - Detect when materials 2/3 are not applicable (empty Type2/Type3)

Implementation notes:
- Use the ExcelService from Step 1.1 for low-level operations
- Implement proper error handling and logging
- Add validation for input ranges (OD ≤600, ID ≥5, etc.)
- Handle the automatic refresh retry logic
- Map Excel cell names to business concepts

Testing approach:
- Unit tests with mocked ExcelService
- Integration tests with real Excel workbook
- Test all error scenarios defined in spec
- Verify input validation logic
```

#### Step 1.3: Pydantic Models and Validation
```
Create the Pydantic models for request/response validation and implement business logic validation that depends on workbook state.

Requirements:
- Define Pydantic models matching the OpenAPI spec exactly
- Implement custom validators for business rules
- Create validation logic that checks workbook state for dynamic rules
- Add proper error messages and field validation

Key components to implement:
1. Request models:
   - EstimateRequest with all fields (profile, measure, od, id, ch, extra, materials, refresh)
   - Field validators for ranges, decimal places, required combinations
   - Custom validator for "extra required when extratype appears" rule

2. Response models:
   - EstimateResponse with nested structure matching spec
   - ProfileOutputs, Measure1/Measure2, Part, Price, Materials models
   - Proper nullable fields for optional materials

3. Validation enums and constants:
   - VALID_PROFILES list
   - VALID_MEASURES list
   - VALID_MATERIALS list with descriptions
   - Validation constants (OD max 600, ID min 5, decimal places)

4. Dynamic validation logic:
   - Validator that checks if extratype is present in workbook state
   - Material validation against available options
   - Cross-field validation (e.g., ID < OD)

Implementation notes:
- Use Pydantic v2 syntax and features
- Implement custom validators using @field_validator and @model_validator
- Add proper error messages that can be surfaced to users
- Handle nullable fields correctly in response models
- Validate decimal precision (3 decimal places)

Testing approach:
- Unit tests for each validator
- Test valid and invalid request combinations
- Test response model serialization
- Verify error messages are user-friendly
```

### Phase 2: Resource Management & Session Handling

#### Step 2.1: Session Management
```
Implement the UUID-based session system for managing temporary workbook copies with proper lifecycle management.

Requirements:
- Create a SessionManager that handles workbook copying and cleanup
- Implement UUID-based temporary file naming
- Add automatic cleanup of expired sessions (5 minute lifespan)
- Handle concurrent sessions safely
- Manage temporary directory operations

Key components to implement:
1. SessionManager class with methods:
   - create_session(session_id) -> creates temp workbook copy
   - get_session_workbook_path(session_id) -> returns temp file path
   - cleanup_session(session_id) -> removes temp workbook
   - cleanup_expired_sessions() -> removes files older than 5 minutes
   - cleanup_all_sessions() -> emergency cleanup

2. File operations:
   - Copy master workbook to %TEMP%\{uuid}.xlsm
   - Track session creation timestamps
   - Safe file deletion with error handling
   - Directory scanning for cleanup operations

3. Configuration:
   - Read TMP_DIR from environment (default %TEMP%)
   - Read WORKBOOK_PATH for master file
   - Set 5-minute session timeout

4. Error handling:
   - Handle file copy failures
   - Manage disk space issues
   - Deal with file locking problems
   - Log all session operations

Implementation notes:
- Use pathlib for cross-platform path handling
- Implement thread-safe operations for concurrent access
- Add proper logging for session lifecycle events
- Handle Windows-specific temporary directory behavior
- Include file permission and access error handling

Testing approach:
- Unit tests for session creation/cleanup
- Test concurrent session handling
- Test cleanup of expired sessions
- Test error scenarios (disk full, permission denied)
- Integration tests with actual file operations
```

#### Step 2.2: Resource Monitoring and Alerts
```
Implement RAM monitoring with email alerts and emergency cleanup procedures as specified in the resource guardrails.

Requirements:
- Create a ResourceMonitor that tracks system RAM usage
- Implement email alerting via Mailgun when RAM exceeds thresholds
- Add emergency cleanup procedures for high memory situations
- Create a background monitoring service

Key components to implement:
1. ResourceMonitor class with methods:
   - get_ram_usage_percent() -> current RAM percentage
   - check_ram_threshold() -> compares against MAX_RAM_PERCENT
   - send_alert_email() -> sends Mailgun email
   - emergency_cleanup() -> terminates Excel processes and purges temp files
   - start_monitoring() -> begins background monitoring

2. Email alerting:
   - Configure Mailgun API client
   - Send <NAME_EMAIL>
   - Use subject "api high-memory alert"
   - <NAME_EMAIL>
   - Include RAM percentage and timestamp in email body

3. Emergency cleanup procedures:
   - Terminate all Excel processes (excel.exe)
   - Delete all *.xlsm files from temp directory
   - Log cleanup actions
   - Continue service operation (never deny requests)

4. Background monitoring:
   - Sample RAM every 30 seconds
   - Compare against MAX_RAM_PERCENT (default 80%)
   - Trigger alerts and cleanup at 90%
   - Use threading or asyncio for background operation

Implementation notes:
- Use psutil for cross-platform RAM monitoring
- Implement Mailgun API integration for email alerts
- Use subprocess or psutil for process termination
- Add proper error handling for cleanup operations
- Include configuration via environment variables

Testing approach:
- Unit tests with mocked system resources
- Test email sending functionality
- Test emergency cleanup procedures
- Test background monitoring loop
- Integration tests with actual resource monitoring
```

### Phase 3: REST API Implementation

#### Step 3.1: Django Ninja API Setup
```
Set up the Django Ninja REST API with authentication, basic endpoints, and proper error handling structure.

Requirements:
- Configure Django Ninja for the REST API
- Implement API key authentication
- Create basic endpoint structure
- Set up proper error handling and logging
- Configure CORS and other necessary middleware

Key components to implement:
1. Django Ninja API configuration:
   - Create api.py with NinjaAPI instance
   - Configure OpenAPI documentation
   - Set up proper URL routing
   - Add API versioning if needed

2. Authentication:
   - Implement X-API-Key header validation
   - Create authentication dependency
   - Add proper error responses for invalid keys
   - Read API key from environment variables

3. Basic endpoint structure:
   - /estimate endpoint (POST)
   - /health endpoint (GET, no auth)
   - Proper HTTP status codes
   - Consistent error response format

4. Error handling:
   - Global exception handlers
   - Proper HTTP status codes (422, 500, 503)
   - Consistent error response format matching spec
   - Logging of all errors

Implementation notes:
- Use Django Ninja's built-in features for validation
- Implement proper dependency injection for services
- Add request/response logging
- Configure proper CORS headers if needed
- Set up OpenAPI spec generation

Testing approach:
- Unit tests for authentication
- Test endpoint routing
- Test error handling
- Test OpenAPI spec generation
- Integration tests with test client
```

#### Step 3.2: Health Check Endpoint
```
Implement the /health endpoint that validates Excel automation is working by creating, opening, and closing a test workbook.

Requirements:
- Create a health check endpoint that doesn't require authentication
- Test the complete Excel automation pipeline
- Return proper status responses
- Handle all error scenarios gracefully

Key components to implement:
1. Health check endpoint:
   - GET /health (no authentication required)
   - Returns {"status": "ok"} with 200 on success
   - Returns error details with 500 on failure

2. Health check logic:
   - Generate dummy session ID
   - Create temporary workbook copy using SessionManager
   - Open workbook using ExcelService
   - Perform basic read operation
   - Close workbook and cleanup session
   - Measure operation time

3. Error handling:
   - Catch all Excel-related errors
   - Catch file system errors
   - Return appropriate error messages
   - Log health check results

4. Performance monitoring:
   - Track health check duration
   - Log slow health checks
   - Include timing in response if needed

Implementation notes:
- Use the same SessionManager and ExcelService as main API
- Keep health check operations minimal but representative
- Add proper cleanup even on errors
- Include health check in monitoring/alerting
- Make health check fast (< 2 seconds)

Testing approach:
- Unit tests with mocked services
- Integration tests with real Excel
- Test error scenarios
- Test performance under load
- Test concurrent health checks
```

#### Step 3.3: Estimate Endpoint Implementation
```
Implement the main /estimate endpoint with complete request processing, validation, Excel automation, and response formatting.

Requirements:
- Create the POST /estimate endpoint with full functionality
- Implement request validation including dynamic workbook-based rules
- Process requests through the complete Excel automation pipeline
- Return properly formatted responses matching the spec exactly

Key components to implement:
1. Estimate endpoint:
   - POST /estimate with authentication required
   - Accept EstimateRequest model
   - Return EstimateResponse model
   - Handle x-excel-session-id header

2. Request processing pipeline:
   - Validate API key and session ID
   - Create or reuse session workbook
   - Validate request against workbook state
   - Write inputs to Excel in correct sequence
   - Execute macro if refresh=true
   - Read outputs and format response
   - Handle all error scenarios

3. Dynamic validation:
   - Check if extratype is present in workbook
   - Validate extra field is provided when required
   - Validate material selections against available options
   - Check input ranges and formats

4. Error handling:
   - Sheet errors (costpriceevco=0, specific partdesc messages)
   - VBA runtime errors via pywinauto
   - Timeout errors (5 second limit)
   - Validation errors
   - System errors (memory, file access)

Implementation notes:
- Use dependency injection for services
- Implement proper transaction-like behavior
- Add comprehensive logging
- Handle the "refresh data" retry logic automatically
- Ensure proper cleanup on all error paths

Testing approach:
- Unit tests with mocked services
- Integration tests with real Excel workbook
- Test all error scenarios from spec
- Test concurrent requests
- Performance testing for 5-second timeout
```

### Phase 4: Integration & Testing

#### Step 4.1: Service Integration and Wiring
```
Wire all services together with simple Pythonic patterns, configuration management, and ensure clean separation of concerns.

Requirements:
- Wire services together using simple Python patterns (constructor injection, factory functions)
- Configure all environment variables and settings
- Ensure clean service boundaries and testability
- Add comprehensive logging and monitoring

Key components to implement:
1. Service wiring:
   - Create service instances in Django settings or a simple factory module
   - Pass dependencies through constructors (e.g., ExcelService needs config, WorkbookState needs ExcelService)
   - Use simple factory functions or module-level instances
   - Keep it simple and Pythonic - no DI frameworks

2. Settings management:
   - Read all environment variables in Django settings
   - Provide sensible defaults
   - Validate configuration on startup
   - Add configuration documentation

3. Service integration:
   - Wire ExcelService, WorkbookState, SessionManager, ResourceMonitor together
   - Ensure proper error propagation between services
   - Add service health monitoring
   - Implement graceful shutdown

4. Background services:
   - Start ResourceMonitor background thread in Django app ready() method
   - Add session cleanup scheduling
   - Implement proper service shutdown
   - Handle service restart scenarios

Implementation notes:
- Use simple constructor injection - pass dependencies as parameters
- Create service instances in a services.py module or Django settings
- Add proper service interfaces using Python protocols/ABC if needed
- Implement service health checks
- Add metrics and monitoring hooks
- Ensure thread safety for background services

Testing approach:
- Integration tests for complete request flow
- Test service startup and shutdown
- Test configuration validation
- Test background service operation
- End-to-end testing with real Excel workbook
```

#### Step 4.2: Comprehensive Error Handling and Logging
```
Implement comprehensive error handling, logging, and monitoring to ensure robust production operation.

Requirements:
- Add detailed logging throughout the application
- Implement proper error tracking and alerting
- Add performance monitoring and metrics
- Ensure all error scenarios are handled gracefully

Key components to implement:
1. Logging configuration:
   - Configure structured logging
   - Add request/response logging
   - Log all Excel operations
   - Add performance metrics logging

2. Error handling:
   - Global exception handlers
   - Proper error classification
   - User-friendly error messages
   - Internal error tracking

3. Monitoring:
   - Request duration tracking
   - Excel operation timing
   - Resource usage monitoring
   - Error rate tracking

4. Alerting:
   - High error rate alerts
   - Performance degradation alerts
   - Resource usage alerts
   - Service health alerts

Implementation notes:
- Use structured logging (JSON format)
- Add correlation IDs for request tracking
- Implement proper log levels
- Add monitoring hooks for external systems
- Ensure sensitive data is not logged

Testing approach:
- Test all error scenarios
- Verify log output format
- Test monitoring metrics
- Test alert conditions
- Load testing for performance monitoring
```

#### Step 4.3: Final Integration Testing and Documentation
```
Perform comprehensive integration testing, create deployment documentation, and ensure the system is production-ready.

Requirements:
- Complete end-to-end testing of all functionality
- Create comprehensive documentation
- Verify all requirements from spec are met
- Prepare for production deployment

Key components to implement:
1. Integration testing:
   - Test complete request/response cycle
   - Test all error scenarios
   - Test concurrent operations
   - Test resource limits and cleanup
   - Performance testing under load

2. Documentation:
   - API documentation (OpenAPI spec)
   - Deployment guide
   - Configuration reference
   - Troubleshooting guide
   - Monitoring and alerting setup

3. Production readiness:
   - Security review
   - Performance optimization
   - Resource usage optimization
   - Error handling verification

4. Validation against spec:
   - Verify all endpoints match spec
   - Verify all error conditions are handled
   - Verify all configuration options work
   - Verify all monitoring and alerting works

Implementation notes:
- Use automated testing for regression prevention
- Document all configuration options
- Include example requests/responses
- Add troubleshooting guides
- Verify Windows service deployment

Testing approach:
- Full end-to-end test suite
- Load testing with multiple concurrent users
- Stress testing for resource limits
- Failure scenario testing
- Production-like environment testing
```

---

## Success Criteria

Each step should be considered complete when:
1. All functionality described in the prompt is implemented
2. Unit tests pass with good coverage
3. Integration tests demonstrate the feature works end-to-end
4. Code follows best practices and is well-documented
5. Error handling is comprehensive and tested
6. Performance meets the specified requirements (5-second timeout, etc.)

## Notes for Implementation

- Each step builds incrementally on previous steps
- No orphaned or hanging code - everything integrates
- Focus on robust error handling throughout
- Maintain clean separation between Excel automation and REST API concerns
- Prioritize testability and maintainability
- Follow the exact specifications for request/response formats
- Implement all resource management and monitoring requirements